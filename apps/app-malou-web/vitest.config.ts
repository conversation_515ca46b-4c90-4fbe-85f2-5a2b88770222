/* eslint-disable @typescript-eslint/naming-convention */
/// <reference types="vitest" />
import path from 'path';
import { defineConfig } from 'vitest/config';

export default defineConfig(({ mode }) => ({
    plugins: [
        {
            name: 'angular-coverage-exclude',
            configureVitest(context): void {
                context.project.config.coverage.exclude = ['**/*.{test,spec}.?(c|m)ts'];
            },
        },
    ],
    test: {
        globals: true,
        setupFiles: ['vitest-setup.js'],
        environment: 'jsdom',
        include: ['src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
        reporters: ['default'],
        coverage: {
            enabled: false,
            excludeAfterRemap: true,
        },
    },
    define: {
        'import.meta.vitest': mode !== 'production',
    },
    resolve: {
        alias: {
            ':modules': path.resolve(__dirname, './src/app/modules'),
            ':shared': path.resolve(__dirname, './src/app/shared'),
            ':assets': path.resolve(__dirname, './src/assets'),
            ':environments': path.resolve(__dirname, './src/environments'),
            ':core': path.resolve(__dirname, './src/app/core'),
        },
    },
}));
