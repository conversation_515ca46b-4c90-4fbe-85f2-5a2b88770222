import { HttpClientTestingModule } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { JwtHelperService } from '@auth0/angular-jwt';
import { of } from 'rxjs';
import { vi } from 'vitest';

import { environment } from ':environments/environment';
import { LocalStorageKey } from ':shared/enums/local-storage-key';

import { AuthService } from './auth.service';

describe('AuthService', () => {
    let authService: AuthService;
    let mockJwtHelperService: Partial<JwtHelperService>;

    beforeEach(() => {
        // Create mocks for dependencies
        mockJwtHelperService = {
            decodeToken: vi.fn(),
            isTokenExpired: vi.fn().mockReturnValue(false), // Default to not expired
        };

        TestBed.configureTestingModule({
            imports: [HttpClientTestingModule],
            providers: [AuthService, { provide: JwtHelperService, useValue: mockJwtHelperService }],
        });

        authService = TestBed.inject(AuthService);

        window.onbeforeunload = vi.fn();
    });

    it('should login', () => {
        const mockResponse = {
            msg: 'Signed in',
            data: { token: 'some jwt token' },
        };

        // Mock the HTTP post call
        (authService._http.post as any) = vi.fn().mockReturnValue(of(mockResponse));

        authService.login({ email: '<EMAIL>', password: 'password' }).subscribe((data) => {
            expect(data).toEqual(mockResponse);
            expect(localStorage.getItem(LocalStorageKey.JWT_TOKEN)).toEqual('some jwt token');
        });

        // Verify the HTTP call was made with correct parameters
        expect(authService._http.post).toHaveBeenCalledWith(environment.APP_MALOU_API_URL + '/api/v1/users/login', {
            email: '<EMAIL>',
            password: 'password',
        });
    });

    it('should logout', () => {
        const mockResponse = { msg: 'Signed out' };

        // simulate user logged in
        localStorage.setItem(LocalStorageKey.JWT_TOKEN, 'a jwt token');

        // Mock the HTTP post call
        (authService._http.post as any) = vi.fn().mockReturnValue(of(mockResponse));

        // log out
        authService.logout$().subscribe((data) => {
            expect(data).toEqual(mockResponse);
        });

        // Verify the HTTP call was made with correct parameters
        expect(authService._http.post).toHaveBeenCalledWith(
            environment.APP_MALOU_API_URL + '/api/v1/users/logout',
            {},
            { withCredentials: true }
        );
    });
});
