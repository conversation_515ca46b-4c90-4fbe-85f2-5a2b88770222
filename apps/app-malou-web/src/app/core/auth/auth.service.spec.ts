import { HttpClient } from '@angular/common/http';
import { JwtHelperService } from '@auth0/angular-jwt';
import { of } from 'rxjs';
import { vi } from 'vitest';

import { environment } from ':environments/environment';
import { LocalStorageKey } from ':shared/enums/local-storage-key';

import { AuthService } from './auth.service';

describe('AuthService', () => {
    let authService: AuthService;
    let mockHttpClient: Partial<HttpClient>;
    let mockJwtHelperService: Partial<JwtHelperService>;

    beforeEach(() => {
        // Create mocks for dependencies
        mockHttpClient = {
            post: vi.fn(),
            get: vi.fn(),
        };

        mockJwtHelperService = {
            decodeToken: vi.fn(),
            isTokenExpired: vi.fn().mockReturnValue(false), // Default to not expired
        };

        // Manually instantiate the service with mocked dependencies
        authService = new AuthService(mockHttpClient as HttpClient, mockJwtHelperService as JwtHelperService);

        window.onbeforeunload = vi.fn();
    });

    it('should login', () => {
        const mockResponse = {
            msg: 'Signed in',
            data: { token: 'some jwt token' },
        };

        // Mock the HTTP post call
        (mockHttpClient.post as any) = vi.fn().mockReturnValue(of(mockResponse));

        authService.login({ email: '<EMAIL>', password: 'password' }).subscribe((data) => {
            expect(data).toEqual(mockResponse);
            expect(localStorage.getItem(LocalStorageKey.JWT_TOKEN)).toEqual('some jwt token');
        });

        // Verify the HTTP call was made with correct parameters
        expect(mockHttpClient.post).toHaveBeenCalledWith(environment.APP_MALOU_API_URL + '/api/v1/users/login', {
            email: '<EMAIL>',
            password: 'password',
        });
    });

    it('should logout', () => {
        const mockResponse = { msg: 'Signed out' };

        // simulate user logged in
        localStorage.setItem(LocalStorageKey.JWT_TOKEN, 'a jwt token');

        // Mock the HTTP post call
        (mockHttpClient.post as any) = vi.fn().mockReturnValue(of(mockResponse));

        // log out
        authService.logout$().subscribe((data) => {
            expect(data).toEqual(mockResponse);
        });

        // Verify the HTTP call was made with correct parameters
        expect(mockHttpClient.post).toHaveBeenCalledWith(
            environment.APP_MALOU_API_URL + '/api/v1/users/logout',
            {},
            { withCredentials: true }
        );
    });
});
