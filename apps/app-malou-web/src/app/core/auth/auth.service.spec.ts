import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { JwtHelperService } from '@auth0/angular-jwt';
import { provideMockStore } from '@ngrx/store/testing';
import { vi } from 'vitest';

import { environment } from ':environments/environment';
import { LocalStorageKey } from ':shared/enums/local-storage-key';

import { AuthService } from './auth.service';

describe('AuthService', () => {
    let httpTestingController: HttpTestingController;
    let authService: AuthService;

    beforeEach(() => {
        const mockJwtHelperService = { decodeToken: vi.fn() };
        vi.mock('@auth0/angular-jwt', () => ({
            JwtHelperService: vi.fn().mockImplementation(() => mockJwtHelperService),
        }));

        TestBed.configureTestingModule({
            providers: [AuthService, provideMockStore({}), provideHttpClientTesting(), JwtHelperService],
        }).compileComponents();

        httpTestingController = TestBed.inject(HttpTestingController);
        authService = TestBed.inject(AuthService);
        window.onbeforeunload = jasmine.createSpy();
    });

    it('should login', () => {
        authService.login({ email: '<EMAIL>', password: 'password' }).subscribe((data) => {
            expect(data).toEqual({
                msg: 'Signed in',
                data: { token: 'some jwt token' },
            });
            expect(localStorage.jwtToken).toEqual('some jwt token');
        });

        const req = httpTestingController.expectOne(environment.APP_MALOU_API_URL + '/api/v1/users/login');

        expect(req.request.method).toEqual('POST');
        req.flush({
            msg: 'Signed in',
            data: { token: 'some jwt token' },
        });
        httpTestingController.verify();
    });

    it('should logout', () => {
        // simulate user logged in
        localStorage.setItem(LocalStorageKey.JWT_TOKEN, 'a jwt token');
        // log out
        authService.logout$().subscribe((data) => {
            expect(data).toEqual({ msg: 'Signed out' });
            expect(localStorage.getItem(LocalStorageKey.JWT_TOKEN)).toEqual(null);
        });
    });
});
