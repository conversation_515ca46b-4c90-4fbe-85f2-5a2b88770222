import { ResizeMetadataComputationService } from './resize-metadata-computation.service';

describe('ResizeMetadataComputationService', () => {
    const service = new ResizeMetadataComputationService();
    const squareAR = 1;
    const landscapeAR = 16 / 9;
    const portraitAR = 4 / 5;
    [
        // mediaAspectRatio, desiredAspectRatio, width, height, expected
        {
            mediaAspectRatio: squareAR,
            desiredAspectRatio: landscapeAR,
            width: 400,
            height: 400,
            expected: { width: 400, height: 225, aspectRatio: landscapeAR, cropPosition: { top: 87.5, left: 0 } },
        },
        {
            mediaAspectRatio: squareAR,
            desiredAspectRatio: portraitAR,
            width: 400,
            height: 400,
            expected: { width: 320, height: 400, aspectRatio: portraitAR, cropPosition: { top: 0, left: 40 } },
        },
        {
            mediaAspectRatio: landscapeAR,
            desiredAspectRatio: squareAR,
            width: 400,
            height: 225,
            expected: { width: 225, height: 225, aspectRatio: squareAR, cropPosition: { top: 0, left: 87.5 } },
        },
        {
            mediaAspectRatio: landscapeAR,
            desiredAspectRatio: portraitAR,
            width: 400,
            height: 225,
            expected: { width: 180, height: 225, aspectRatio: portraitAR, cropPosition: { top: 0, left: 110 } },
        },
        {
            mediaAspectRatio: portraitAR,
            desiredAspectRatio: squareAR,
            width: 320,
            height: 400,
            expected: { width: 320, height: 320, aspectRatio: squareAR, cropPosition: { top: 40, left: 0 } },
        },
        {
            mediaAspectRatio: portraitAR,
            desiredAspectRatio: landscapeAR,
            width: 320,
            height: 400,
            expected: { width: 320, height: 180, aspectRatio: landscapeAR, cropPosition: { top: 110, left: 0 } },
        },
    ].forEach(({ mediaAspectRatio, desiredAspectRatio, width, height, expected }) => {
        it(`should return the correct resize metadata from aspect ratio ${mediaAspectRatio} to ${desiredAspectRatio}`, () => {
            expect(service.computeResizeMetadata({ mediaAspectRatio, desiredAspectRatio, width, height })).toEqual(expected);
        });
    });
});
