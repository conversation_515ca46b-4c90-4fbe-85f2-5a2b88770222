import { cloneDeep } from 'lodash';

import { AggregationTimeScale, MalouMetric, PlatformKey } from '@malou-io/package-utils';

import { ReviewRatingInsightsByRestaurantId } from ':modules/aggregated-statistics/e-reputation/reviews/reviews-rating-kpis/review-rating-insights-by-restaurant-id/review-rating-insights-by-restaurant-id';
import { InsightsByPlatformByRestaurant } from ':shared/models';

describe('ReviewRatingInsightsByRestaurantId', () => {
    describe('fromDto', () => {
        it('should map dto to ReviewRatingInsightsByRestaurantId', () => {
            const dto = _buildDefaultReviewRatingInsightsByRestaurantIdDto(['restaurantId1', 'restaurantId2'], [PlatformKey.GMB]);

            const result = ReviewRatingInsightsByRestaurantId.fromDto(dto);

            expect(result).toBeInstanceOf(ReviewRatingInsightsByRestaurantId);
        });
    });

    describe('merge', () => {
        it('should merge two ReviewRatingInsightsByRestaurantId with different restaurantId into one ReviewRatingInsightsByRestaurantId with two restaurantId', () => {
            const dto1 = _buildDefaultReviewRatingInsightsByRestaurantIdDto(['restaurantId1'], [PlatformKey.GMB]);
            const reviewInsights1 = ReviewRatingInsightsByRestaurantId.fromDto(dto1);
            const dto2 = _buildDefaultReviewRatingInsightsByRestaurantIdDto(['restaurantId2'], [PlatformKey.GMB]);
            const reviewInsights2 = ReviewRatingInsightsByRestaurantId.fromDto(dto2);

            const result = reviewInsights1.merge(reviewInsights2);

            expect(result.size).toEqual(2);
            expect(result.has('restaurantId1')).toBeTruthy();
            expect(result.has('restaurantId2')).toBeTruthy();
        });

        it('should merge two ReviewRatingInsightsByRestaurantId with same restaurantId into one ReviewRatingInsightsByRestaurantId with one restaurantId with the second data', () => {
            const dto1 = _buildDefaultReviewRatingInsightsByRestaurantIdDto(['restaurantId1'], [PlatformKey.GMB, PlatformKey.TRIPADVISOR]);
            const reviewInsights1 = ReviewRatingInsightsByRestaurantId.fromDto(dto1);
            const dto2 = _buildDefaultReviewRatingInsightsByRestaurantIdDto(['restaurantId1'], [PlatformKey.GMB, PlatformKey.TRIPADVISOR]);
            dto2['restaurantId1'][PlatformKey.GMB] = {
                [AggregationTimeScale.BY_DAY]: {
                    [MalouMetric.PLATFORM_RATING]: [
                        {
                            date: '2021-01-01',
                            value: 4.7,
                        },
                        {
                            date: '2021-01-02',
                            value: 4.8,
                        },
                    ],
                },
            };
            const reviewInsights2 = ReviewRatingInsightsByRestaurantId.fromDto(dto2);

            const result = reviewInsights1.merge(reviewInsights2);

            expect(result.size).toEqual(1);
            expect(result.has('restaurantId1')).toBeTruthy();
            expect(result.get('restaurantId1')).toEqual(dto2['restaurantId1']);
        });
    });

    describe('deleteMany', () => {
        it('should delete many restaurantId from ReviewRatingInsightsByRestaurantId', () => {
            const dto = _buildDefaultReviewRatingInsightsByRestaurantIdDto(
                ['restaurantId1', 'restaurantId2', 'restaurantId3'],
                [PlatformKey.GMB]
            );
            const reviewInsights = ReviewRatingInsightsByRestaurantId.fromDto(dto);

            const result = reviewInsights.deleteMany(['restaurantId1', 'restaurantId2']);

            expect(result.size).toEqual(1);
            expect(result.has('restaurantId1')).toBeFalsy();
            expect(result.has('restaurantId2')).toBeFalsy();
            expect(result.has('restaurantId3')).toBeTruthy();
        });
    });
});

function _buildDefaultReviewRatingInsightsByRestaurantIdDto(
    restaurantIds: string[],
    platforms: PlatformKey[]
): InsightsByPlatformByRestaurant {
    const insightsByPlatforms = platforms.reduce((acc, platform) => {
        acc[platform] = {
            [AggregationTimeScale.BY_DAY]: {
                [MalouMetric.PLATFORM_RATING]: [
                    {
                        date: '2021-01-01',
                        value: 4.5,
                    },
                    {
                        date: '2021-01-02',
                        value: 4.6,
                    },
                ],
            },
        };
        return acc;
    }, {});

    return restaurantIds.reduce((acc, restaurantId) => {
        acc[restaurantId] = {
            ...cloneDeep(insightsByPlatforms),
        };
        return acc;
    }, {});
}
