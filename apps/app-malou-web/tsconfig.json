{"extends": "../../tsconfig.options.json", "angularCompilerOptions": {"strictTemplates": true}, "compileOnSave": false, "compilerOptions": {"rootDir": ".", "outDir": "./dist", "esModuleInterop": true, "lib": ["es2019", "dom"], "module": "es2020", "noImplicitThis": true, "composite": false, "noImplicitAny": false, "strict": false, "isolatedModules": false, "baseUrl": "./src", "paths": {":core/*": ["app/core/*"], ":environments/*": ["environments/*"], ":assets/*": ["assets/*"], ":shared/*": ["app/shared/*"], ":modules/*": ["app/modules/*"]}, "sourceRoot": "/", "strictNullChecks": true, "target": "ES2022", "typeRoots": ["node_modules/@types", "webpack-env"], "useDefineForClassFields": false}, "references": [{"path": "../../packages/malou-utils"}, {"path": "../../packages/malou-dto"}], "include": ["src", "vitest.config.ts", "vitest-setup.js", "tailwind.config.js"]}