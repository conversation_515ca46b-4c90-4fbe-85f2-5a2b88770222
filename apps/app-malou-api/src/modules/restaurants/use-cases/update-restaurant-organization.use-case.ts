import assert from 'node:assert';
import { singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import { BusinessCategory, MalouErrorCode } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import OrganizationsRepository from ':modules/organizations/organizations.repository';
import CreateLocationUseCase from ':modules/publishers/yext/use-cases/create-location/create-location.use-case';
import DeleteLocationUseCase from ':modules/publishers/yext/use-cases/delete-location/delete-location.use-case';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

@singleton()
export class UpdateRestaurantOrganizationUseCase {
    constructor(
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _organizationsRepository: OrganizationsRepository,
        private readonly _createLocationUseCase: CreateLocationUseCase,
        private readonly _deleteLocationUseCase: DeleteLocationUseCase
    ) {}

    async execute(restaurantId: string, organizationId: string): Promise<void> {
        const organization = await this._organizationsRepository.findOne({ filter: { _id: organizationId }, options: { lean: true } });

        if (!organization) {
            throw new MalouError(MalouErrorCode.ORGANIZATION_NOT_FOUND, {
                message: 'Cannot update restaurant organization : organization not found',
            });
        }

        const restaurant = await this._restaurantsRepository.findOne({ filter: { _id: restaurantId }, options: { lean: true } });
        if (!restaurant) {
            throw new MalouError(MalouErrorCode.RESTAURANT_NOT_FOUND, {
                message: 'Cannot update restaurant organization : restaurant not found',
            });
        }

        try {
            await this._deleteLocationUseCase.execute(restaurant._id.toString());
        } catch (error) {
            if (error instanceof MalouError && MalouErrorCode.YEXT_LOCATION_NOT_FOUND === error.malouErrorCode) {
                // Do nothing
            } else {
                throw error;
            }
        }

        const updatedRestaurant = await this._restaurantsRepository.findOneAndUpdate({
            filter: { _id: toDbId(restaurantId) },
            update: { organizationId: toDbId(organizationId) },
            options: { lean: true, new: true },
        });
        assert(updatedRestaurant, 'Did not find restaurant');

        if (updatedRestaurant.type !== BusinessCategory.BRAND) {
            await this._createLocationUseCase.execute(updatedRestaurant);
        }
    }
}
