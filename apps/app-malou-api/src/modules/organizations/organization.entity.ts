import { RemoveMethodsFromClass } from '@malou-io/package-utils';

export type OrganizationProps = RemoveMethodsFromClass<Organization>;

export class Organization {
    id: string;
    name: string;
    providerId?: string;
    verifiedEmailsForCampaigns?: string[];
    limit?: number | null;
    createdAt?: Date;
    updatedAt?: Date;

    constructor(organization: OrganizationProps) {
        this.id = organization.id;
        this.name = organization.name;
        this.providerId = organization.providerId;
        this.verifiedEmailsForCampaigns = organization.verifiedEmailsForCampaigns;
        this.limit = organization.limit;
        this.createdAt = organization.createdAt;
        this.updatedAt = organization.updatedAt;
    }

    toDto(): OrganizationProps {
        return {
            id: this.id,
            name: this.name,
            providerId: this.providerId,
            verifiedEmailsForCampaigns: this.verifiedEmailsForCampaigns,
            limit: this.limit,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
        };
    }
}
