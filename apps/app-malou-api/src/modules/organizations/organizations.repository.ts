import { singleton } from 'tsyringe';

import { EntityRepository, IOrganization, OrganizationModel, toDbId } from '@malou-io/package-models';

import { Organization } from ':modules/organizations/organization.entity';

@singleton()
export default class OrganizationsRepository extends EntityRepository<IOrganization> {
    constructor() {
        super(OrganizationModel);
    }

    async getOrganizationById(organizationId: string): Promise<Organization | null> {
        const organizationDocument = await this.findOne({
            filter: { _id: toDbId(organizationId) },
            options: { lean: true },
        });
        if (!organizationDocument) {
            return null;
        }
        return this._toEntity(organizationDocument);
    }

    private _toEntity(organization: IOrganization): Organization {
        return new Organization({
            id: organization._id.toString(),
            name: organization.name,
            providerId: organization.providerId,
            verifiedEmailsForCampaigns: organization.verifiedEmailsForCampaigns,
            createdAt: organization.createdAt,
            updatedAt: organization.updatedAt,
        });
    }
}
