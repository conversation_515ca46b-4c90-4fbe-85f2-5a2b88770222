import { NextFunction, Response, Router } from 'express';
import { singleton } from 'tsyringe';

import AbstractRouter from ':helpers/abstracts/abstract-router';
import { RequestWithRawBody } from ':helpers/utils.types';

import WebhooksController from './webhooks.controller';

@singleton()
export default class WebhooksRouter extends AbstractRouter {
    constructor(private _webhooksController: WebhooksController) {
        super();
    }

    init(): Router {
        this.router.use(this._webhooksController.handleCheckForRedirection);

        this.router.get(
            ['/facebook/page', '/facebook/instagram', '/facebook/permissions'],
            this._webhooksController.handleFacebookWebhookVerification
        );

        this.router.post('/facebook/page', this._webhooksController.handleFacebookPageWebhooks);

        this.router.post('/facebook/permissions', this._webhooksController.handleFacebookPermissionsWebhooks);

        this.router.post('/facebook/instagram', this._webhooksController.handleInstagramWebhooks);

        this.router.post('/tiktok/events', (req: RequestWithRawBody, res: Response, next: NextFunction) =>
            this._webhooksController.handleTiktokPostEvents(req, res, next)
        );
        this.router.post('/jimo', (req: RequestWithRawBody, res: Response, next: NextFunction) =>
            this._webhooksController.handleJimoEvents(req, res, next)
        );

        this.router.post('/malou/new-organization', (req, res, next) =>
            this._webhooksController.handleMalouNewOrganization(req, res, next)
        );

        return this.router;
    }
}
