import { container } from 'tsyringe';

import * as facebookCredentialsUseCases from '../../facebook.use-cases';
import { GetSearchedPageUseCase } from './get-searched-page.use-case';

describe('GetSearchedPageUseCase', () => {
    let getSearchedPageUseCase: GetSearchedPageUseCase;

    beforeEach(() => {
        container.clearInstances();
        getSearchedPageUseCase = container.resolve(GetSearchedPageUseCase);
    });

    describe('execute', () => {
        afterEach(() => {
            jest.restoreAllMocks();
        });

        it('should return pages for a simple query', async () => {
            const mockPages = [
                { id: '1', name: 'Page 1', link: 'link1', location: { city: 'Paris' } },
                { id: '2', name: 'Page 2', link: 'link2', location: { city: 'Lyon' } },
            ];
            const spy = jest.spyOn(facebookCredentialsUseCases, 'pagesSearch').mockResolvedValueOnce(mockPages);
            jest.spyOn(facebookCredentialsUseCases, 'pagesSearch').mockResolvedValue([]);
            jest.spyOn(facebookCredentialsUseCases, 'pagesSearch').mockResolvedValue([]);
            const params = {
                query: 'test',
                onlyWithLocation: false,
            };
            const result = await getSearchedPageUseCase.execute(params);
            expect(result).toEqual(mockPages);
            expect(spy).toHaveBeenCalledWith(
                'test',
                expect.objectContaining({ onlyWithLocation: false, whitelistedPageIds: [], wantedPlatformSocialId: undefined })
            );
        });

        it('should try without restaurant word if no result and wantedPlatformSocialId is set', async () => {
            const mockPages = [];
            const mockPagesWithoutRestaurant = [{ id: '3', name: 'Page 3', link: 'link3', location: { city: 'Marseille' } }];
            const spy = jest.spyOn(facebookCredentialsUseCases, 'pagesSearch').mockResolvedValueOnce(mockPages);
            const spy2 = jest.spyOn(facebookCredentialsUseCases, 'pagesSearch').mockResolvedValueOnce(mockPagesWithoutRestaurant);
            const params = {
                query: 'restaurant test',
                onlyWithLocation: true,
                wantedPlatformSocialId: '3',
            };
            const result = await getSearchedPageUseCase.execute(params);
            expect(result).toEqual(mockPagesWithoutRestaurant);
            expect(spy).toHaveBeenCalledWith(
                'restaurant test',
                expect.objectContaining({ onlyWithLocation: true, whitelistedPageIds: [], wantedPlatformSocialId: '3' })
            );
            expect(spy2).toHaveBeenCalledWith(
                'test',
                expect.objectContaining({ onlyWithLocation: true, whitelistedPageIds: [], wantedPlatformSocialId: '3' })
            );
        });

        it('should try with city if no result after removing restaurant word', async () => {
            const mockPages = [];
            const spy1 = jest.spyOn(facebookCredentialsUseCases, 'pagesSearch').mockResolvedValueOnce(mockPages);
            const spy2 = jest.spyOn(facebookCredentialsUseCases, 'pagesSearch').mockResolvedValueOnce(mockPages);
            const mockPagesWithCity = [{ id: '4', name: 'Page 4', link: 'link4', location: { city: 'Nice' } }];
            const spy3 = jest.spyOn(facebookCredentialsUseCases, 'pagesSearch').mockResolvedValueOnce(mockPagesWithCity);
            const params = {
                query: 'restaurant test',
                onlyWithLocation: true,
                city: 'Nice',
                wantedPlatformSocialId: '4',
            };
            const result = await getSearchedPageUseCase.execute(params);
            expect(result).toEqual(mockPagesWithCity);
            expect(spy1).toHaveBeenCalledWith(
                'restaurant test',
                expect.objectContaining({ onlyWithLocation: true, whitelistedPageIds: [], wantedPlatformSocialId: '4' })
            );
            expect(spy2).toHaveBeenCalledWith(
                'test',
                expect.objectContaining({ onlyWithLocation: true, whitelistedPageIds: [], wantedPlatformSocialId: '4' })
            );
            expect(spy3).toHaveBeenCalledWith(
                'restaurant test Nice',
                expect.objectContaining({ onlyWithLocation: true, whitelistedPageIds: [], wantedPlatformSocialId: '4' })
            );
        });

        it('should return empty array if no results at all', async () => {
            const spy = jest.spyOn(facebookCredentialsUseCases, 'pagesSearch').mockResolvedValue([]);
            const params = {
                query: 'noresult',
                onlyWithLocation: true,
            };
            const result = await getSearchedPageUseCase.execute(params);
            expect(result).toEqual([]);
            expect(spy).toHaveBeenCalledWith(
                'noresult',
                expect.objectContaining({ onlyWithLocation: true, whitelistedPageIds: [], wantedPlatformSocialId: undefined })
            );
        });
    });
});
