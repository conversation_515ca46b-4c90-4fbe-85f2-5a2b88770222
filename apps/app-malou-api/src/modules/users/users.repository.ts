import { Aggregate, FilterQuery } from 'mongoose';

import {
    DbId,
    EntityRepository,
    ID,
    IOrganization,
    IUser,
    IUserRestaurant,
    OverwriteOrAssign,
    ReadPreferenceMode,
    toDbId,
    toDbIds,
    UserModel,
} from '@malou-io/package-models';

import { User } from ':modules/users/entities/user.entity';

type FilteredUsersWithRestaurants = OverwriteOrAssign<
    IUser,
    {
        restaurants: OverwriteOrAssign<
            IUserRestaurant,
            {
                restaurantDetails: { _id: string; name: string; active: boolean };
            }
        >[];
        organizations: IOrganization[];
    }
>[];

export class UsersRepository extends EntityRepository<IUser> {
    constructor() {
        super(UserModel);
    }

    getUserDevicesTokens = async (userId: string): Promise<string[]> => {
        const user = await this.findOne({
            filter: { _id: toDbId(userId) },
            options: { lean: true },
            projection: { 'settings.notifications.mobile.userDevicesTokens': 1 },
        });
        if (!user) {
            return [];
        }
        return user.settings.notifications.mobile.userDevicesTokens;
    };

    /**
     * Return user with userRestaurants, active restaurant and organization details
     */
    getFilteredUserRestaurants = (params: Record<string, any>): Aggregate<FilteredUsersWithRestaurants> =>
        this.aggregate([
            {
                $match: params,
            },
            {
                $lookup: {
                    from: 'userrestaurants',
                    localField: '_id',
                    foreignField: 'userId',
                    as: 'restaurants',
                    pipeline: [
                        {
                            $lookup: {
                                from: 'restaurants',
                                localField: 'restaurantId',
                                foreignField: '_id',
                                as: 'restaurantDetails',
                                pipeline: [
                                    {
                                        $project: {
                                            _id: 1,
                                            name: 1,
                                            active: 1,
                                        },
                                    },
                                ],
                            },
                        },
                        {
                            $match: { 'restaurantDetails.active': true },
                        },
                        {
                            // Unwind restaurantDetails array, as there is only one element
                            $addFields: {
                                restaurantDetails: { $arrayElemAt: ['$restaurantDetails', 0] },
                            },
                        },
                    ],
                },
            },
            {
                $lookup: {
                    from: 'organizations',
                    localField: 'organizationIds',
                    foreignField: '_id',
                    as: 'organizations',
                },
            },
            {
                $addFields: {
                    password: null,
                },
            },
        ]);

    createUser = (user: Partial<IUser>): Promise<IUser> => {
        return this.create({
            data: user,
        });
    };

    async getUsersByIds(ids: string[]): Promise<User[]> {
        const docs = await this.find({
            filter: { _id: { $in: toDbIds(ids) } },
            options: { lean: true },
        });

        return docs.map((doc) => this.toEntity(doc));
    }

    async findById(id: string): Promise<User | null> {
        const doc = await this.findOne({
            filter: { _id: toDbId(id) },
            options: { lean: true },
        });

        return doc ? this.toEntity(doc) : null;
    }

    async removeRestaurantFromUserNotificationFilters({ restaurantId, userId }: { restaurantId: string; userId: string }): Promise<void> {
        await this.findOneAndUpdate({
            filter: { _id: userId },
            update: { $pull: { 'settings.notifications.web.filters.restaurantIds': toDbId(restaurantId) } },
        });
    }

    async addRestaurantToUserNotificationFilters({ restaurantId, userId }: { restaurantId: string; userId: string }): Promise<void> {
        await this.findOneAndUpdate({
            filter: { _id: userId },
            update: { $addToSet: { 'settings.notifications.web.filters.restaurantIds': toDbId(restaurantId) } },
        });
    }

    /**
     * get single user by 'filter' including password hash
     */
    getUserWithPasswordHash = (params: FilterQuery<any>) =>
        UserModel.findOne(params)
            .select('+password')
            .then((doc): any => doc);

    /**
     * find user by id then update it
     */
    updateUserById = (id: DbId, params: FilterQuery<IUser>, options = {}): any =>
        UserModel.findOne({ filter: { _id: id }, options: { populate: [{ path: 'profilePicture' }] } }).updateOne({
            filter: { _id: id },
            update: params,
            options,
        });

    countUsersInOrganizations = (organizationIds: DbId[]) =>
        this.aggregate([
            {
                $match: { organizationIds: { $elemMatch: { $in: organizationIds } } },
            },
            { $count: 'count' },
        ]);

    removeDeviceTokenFromUser = async (id: string, deviceToken: string) => {
        const user = await this.findOne({ filter: { _id: toDbId(id) } });
        if (!user) {
            return null;
        }

        const userDevicesTokens = user.settings.notificationSettings.userDevicesTokens;

        user.settings.notificationSettings.userDevicesTokens = userDevicesTokens.filter((token) => token !== deviceToken);

        return this.findOneAndUpdate({ filter: { _id: user._id }, update: user, options: { lean: true } });
    };

    async removeOrganizationFromUser(organizationId: string): Promise<void> {
        const dbId = toDbId(organizationId);
        await this.updateMany({
            filter: { organizationIds: dbId },
            update: { $pull: { organizationIds: dbId } },
        });
    }

    async getUserIdsByEmails(emails: string[]): Promise<string[]> {
        const users = await this.find({
            filter: { email: { $in: emails } },
            projection: { _id: 1 },
            options: { lean: true },
        });
        return users.map((user) => user._id.toString());
    }

    async getUserWithActiveRestaurants(_id: ID) {
        const user = await this.findOne({
            filter: { _id },
            options: {
                populate: [
                    {
                        path: 'restaurants',
                        populate: [
                            {
                                path: 'restaurant',
                                select: 'organizationId active',
                            },
                        ],
                    },
                    {
                        path: 'organizations',
                    },
                    {
                        path: 'profilePicture',
                    },
                ],
                lean: true,
            },
        });
        if (!user) {
            return null;
        }
        return { ...user, restaurants: user?.restaurants?.filter((r) => r.restaurant?.active) };
    }

    //
    /**
     * Filters a list of email addresses, returning only those that are either not associated
     * with any known user in the database or are associated with users who are verified
     * and not have been deactivated by an admin.
     *
     * @param emails - An array of known and unknown email addresses to filter.
     * @returns A promise that resolves to an array of email addresses that are either unknown,
     *          verified, belong to users not deactivated by an admin.
     */
    async filterNonVerifiedKnownEmails(emails: string[]): Promise<string[]> {
        const users = await this.find({
            filter: { email: { $in: emails } },
            projection: { _id: 1, email: 1, verified: 1, hasBeenDeactivatedByAdmin: 1 },
            options: { lean: true, ReadPreference: ReadPreferenceMode.SECONDARY },
        });

        return emails.filter((email) => {
            const malouUser = users.find((user) => user.email === email);
            if (!malouUser) {
                return true;
            }
            return malouUser.verified && !malouUser.hasBeenDeactivatedByAdmin;
        });
    }

    readonly toEntity = (doc: IUser): User =>
        new User({
            id: doc._id.toString(),
            email: doc.email,
            providerId: doc.providerId,
            name: doc.name,
            defaultLanguage: doc.defaultLanguage,
            caslRole: doc.caslRole,
            createdAt: doc.createdAt,
            updatedAt: doc.updatedAt,
            expireSessionBefore: doc.expireSessionBefore,
            hasV3Access: doc.hasV3Access,
            lastname: doc.lastname,
            organizationIds: doc.organizationIds?.map((id) => id.toString()),
            password: doc.password,
            profilePicture: doc.profilePicture?.toString(),
            role: doc.role,
            lastVisitedRestaurantId: doc.lastVisitedRestaurantId?.toString(),
            settings: {
                ...doc.settings,
                notifications: {
                    ...doc.settings.notifications,
                    web: {
                        ...doc.settings.notifications.web,
                        filters: {
                            ...doc.settings.notifications.web.filters,
                            restaurantIds: doc.settings.notifications.web.filters?.restaurantIds?.map((id) => id.toString()),
                        },
                    },
                },
                receiveMessagesNotifications: {
                    ...doc.settings.receiveMessagesNotifications,
                    restaurantsIds: doc.settings.receiveMessagesNotifications?.restaurantsIds?.map((id) => id.toString()),
                },
            },
            shouldExpireAbilitySession: doc.shouldExpireAbilitySession,
            createdByUserId: doc.createdByUserId?.toString(),
            verified: doc.verified,
            hasBeenDeactivatedByAdmin: doc.hasBeenDeactivatedByAdmin,
        });
}
