export enum CountriesWithEvents {
    FR = 'FR',
    GB = 'GB',
    US = 'US',
    CA = 'CA',
    AE = 'AE',
    MA = 'MA',
    LU = 'LU',
    BE = 'BE',
    CH = 'CH',
}

export const CGU_LINK = 'https://welcomehomemalou.notion.site/Consulter-les-CGU-dd62041f02ab459cb101d60c8546a62e';
export const MALOU_PRIVACY_POLICY_FR = 'https://welcomehomemalou.notion.site/FR-4b1d0788b1c848c78ca5bfa20eadbf48';
export const MALOU_PRIVACY_POLICY_EN = 'https://welcomehomemalou.notion.site/EN-a466f0380f604a78a0c46ba20e561612';

export enum MessageStatus {
    RECEIVED = 'RECEIVED',
    DELIVERED = 'DELIVERED',
    READ = 'READ',
    PENDING = 'PENDING',
    ERROR = 'ERROR',
}

export enum ConversationStatus {
    READ = 'READ',
    UNREAD = 'UNREAD',
}

export enum PushNotificationSubject {
    FEEDBACK = 'feedback',
    MESSAGE = 'message',
    REVIEW = 'review',
    POST = 'post',
    GLOBAL = 'global',
    REMINDER = 'reminder',
}

export enum Role {
    ADMIN = 'admin',
    MALOU_BASIC = 'malou-basic',
    MALOU_GUEST = 'malou-guest',
    MALOU_FREE = 'malou-free',
}

export enum UserCaslRole {
    ADMIN = 'admin', // This role bypasses all casl checks. It is never assigned directly to a user but rather replaced as a UserCaslRole when user role is admin
    OWNER = 'owner',
    GUEST = 'guest',
}

export enum CaslRole {
    ADMIN = 'admin', // This role bypasses all casl checks. It is never assigned directly to a userRestaurant but rather replaced as a CaslRole when user role is admin
    OWNER = 'owner',
    MODERATOR = 'moderator',
    EDITOR = 'editor',
    GUEST = 'guest',
}

export enum BusinessCategory {
    LOCAL_BUSINESS = 'local_business',
    BRAND = 'brand',
}

// https://developers.facebook.com/docs/graph-api/guides/error-handling/
export const facebookPageAccessTokenErrorCodes = Object.freeze([190, 460, 463]);

export enum FacebookPageCategory {
    DARK_KITCHEN = 'dark_kitchen', // Fb page with no location locations and parent_page
    BRAND = 'brand', // Fb page with stores (has a locations param)
    STORE = 'store', // Fb page linked to a brand parent page (has a parent_page param)
    INDEPENDANT_LOCATION = 'independant_location', // Fb page with location and no parent_page
}

export enum MaintenanceMode {
    UP = 'up',
    MAINTENANCE = 'maintenance',
}

export const trespassUsers = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
];

export enum BricksCategory {
    VENUE_TYPE = 'venueType',
    VENUE_CATEGORY = 'venueCategory',
    VENUE_SPECIAL = 'venueSpecial',
    VENUE_LOCATION = 'venueLocation',
    VENUE_LABEL = 'venueLabel',
    TOURISTIC_AREA = 'touristicArea',
    STATION = 'station',
    VENUE_OFFER = 'venueOffer',
    VENUE_EQUIPMENT = 'venueEquipment',
    VENUE_ATTRIBUTE = 'venueAttribute',
    VENUE_AUDIENCE = 'venueAudience',
    STANDARD = 'standard',
    RESTAURANT_NAME = 'restaurantName',
    REVIEWER_NAME = 'reviewerName',
}

export enum BricksAiCategory {
    VENUE_TYPE = 'venueType',
    VENUE_CATEGORY = 'venueCategory',
    VENUE_DISH = 'venueDish',
    VENUE_LOCATION = 'venueLocation',
    VENUE_SERVICE = 'venueService',
    VENUE_EQUIPMENT = 'venueEquipment',
    VENUE_CHARACTERISTICS = 'venueCharacteristics',
    VENUE_AUDIENCE = 'venueAudience',
    STANDARD = 'standard',
}

export const defaultHashtagCategories = Object.freeze({
    GENERAL: {
        fr: 'Général',
        en: 'General',
    },
    LOCATION: {
        fr: 'Localisation',
        en: 'Location',
    },
});

export enum CreatedBy {
    FORM_CUSTOMER = 'form_customer',
    FORM_MANAGER = 'form_manager',
}

export enum GeoSamplePlatform {
    GMAPS = 'gmaps',
}

export enum NfcType {
    STICKER = 'sticker',
    TOTEM = 'totem',
}

export enum PostedStatus {
    POSTED = 'posted',
    PENDING = 'pending',
    RETRY = 'retry',
    REJECTED = 'rejected',
}

export enum FilterType {
    REVIEWS = 'reviews',
    COMMENTS = 'comments',
    INSIGHTS = 'insights',
    PLATFORMS_INSIGHTS = 'platforms_insights',
    POSTS = 'posts',
    MEDIA = 'media',
    MENTIONS = 'mentions',
}

export const postsUpdateTexts = Object.freeze({
    GMB: ['Les horaires ont été mises à jour.', 'Hours were updated.'],
    FACEBOOK: [/a changé sa photo de/, /a actualisé son statut/, /updated their profile picture/, /updated their cover photo/],
});

export const facebookNewPhotoLegend = [
    /a une nouvelle photo/,
    /ha añadido una foto nueva/,
    /added a new photo/,
    /さんが写真を追加しました/,
];

export const Langs = Object.freeze({
    FR: { short: 'fr', full: 'french' },
    EN: { short: 'en', full: 'english' },
    ES: { short: 'es', full: 'spanish' },
    IT: { short: 'it', full: 'italian' },
});

export enum MediaCategory {
    PROFILE = 'profile',
    COVER = 'cover',
    ADDITIONAL = 'additional',
}

export enum FileFormat {
    PNG = 'png',
    JPEG = 'jpeg',
    JPG = 'jpg',
    MOV = 'mov',
    QUICKTIME = 'quicktime',
    MP4 = 'mp4',
}

export enum Civility {
    MALE = 'male',
    FEMALE = 'female',
    OTHER = 'other',
}

export enum ContactMode {
    EMAIL = 'email',
    SMS = 'sms',
}

export enum ClientSource {
    MANUAL = 'manual',
    MALOU = 'malou',
    ZENCHEF = 'zenchef',
    ZELTY = 'zelty',
    LAFOURCHETTE = 'lafourchette',
    PULP = 'pulp',
    WHEEL_OF_FORTUNE = 'wheel_of_fortune',
}

export enum CampaignType {
    REVIEW_BOOSTER = 'review_booster',
    PROMOTION = 'promotion',
}

export enum RedirectStar {
    FOUR_STARS = 'four_stars',
    FIVE_STARS = 'five_stars',
}

export enum ClientVariable {
    FIRSTNAME = '#CLIENT_FIRSTNAME#',
    LASTNAME = '#CLIENT_LASTNAME#',
}

// TODO = join with MediaType and use same word for photo/image
export enum SocialAttachmentsMediaTypes {
    IMAGE = 'image',
    VIDEO = 'video',
}

export enum MediaType {
    PHOTO = 'photo',
    VIDEO = 'video',
    FILE = 'file',
}

export enum InputMediaType {
    IMAGE = 'image',
    VIDEO = 'video',
}

export const coverDimensions = Object.freeze({
    cover: [1920, 1080],
    smallCover: [480, 270],
});

export enum DescriptionSize {
    LONG = 'long',
    SHORT = 'short',
}

export const descriptionSize = Object.freeze({
    LONG: {
        key: DescriptionSize.LONG,
        maxlength: 750,
    },
    SHORT: {
        key: DescriptionSize.SHORT,
        maxlength: 100,
    },
});

export enum EmailType {
    ACCESS_UPDATE = 'access_update',
    AI_API_HARD_LIMIT_REACHED = 'ai_api_hard_limit_reached',
    AI_API_SOFT_LIMIT_REACHED = 'ai_api_soft_limit_reached',
    BOOSTER_PACK_SUBSCRIPTION_REQUEST = 'booster_pack_subscription_request',
    CLOSED_FEEDBACK = 'closed_feedback',
    CONFIRM_CREATE_ACCOUNT = 'confirm_create_account',
    CREATE_GUEST_ACCOUNT = 'create_guest_account',
    CREATE_PAGE = 'create_page',
    CREATE_RESTAURANT = 'create_restaurant',
    DOWNLOAD_MOBILE_APP = 'download_mobile_app',
    EMAIL_VERIFICATION = 'email_verification',
    EMPTY_STOCK = 'empty_stock',
    GIFT_EXPIRES_SOON = 'gift_expires_soon',
    NEW_FEEDBACK_MESSAGE = 'new_feedback_message',
    NEW_MESSAGE_RECEIVED = 'new_message_received',
    OPENED_FEEDBACK = 'opened_feedback',
    POST_LOCATION_EXPIRED = 'post_location_expired',
    RESET_PASSWORD = 'reset_password',
    REVIEW_BOOSTER = 'review_booster',
    REVIEW_BOOSTER_TEST = 'review_booster_test',
    REVIEW_REPLY = 'review_reply',
    RETRIEVE_GIFT = 'retrieve_gift',
    UPDATE = 'update',
    USER_REVOKED_FB_CONNECTION = 'user_revoked_fb_connection',
    WOF_LIVE_TOMORROW = 'wof_live_tomorrow',
    WRONG_PLATFORM_ACCESS = 'wrong_platform_access',
}

export enum EmailCategory {
    REVIEW_BOOSTER = 'review_booster',
    ADMIN_NOTIF = 'admin_notif',
    USER_NOTIF = 'user_notif',
    REVIEW_REPLY = 'review_reply',
    REVIEWS_REPORT = 'reviews_report',
    FEEDBACK_NOTIFICATION = 'feedback_notification',
    MESSAGES_NOTIFICATION = 'messages_notification',
    PERMISSIONS = 'permissions',
    POST_NOTIFICATION = 'post_notification',
    AI_NOTIFICATION = 'ai_notification',
    MOBILE_APP_NOTIFICATION = 'mobile_app_notification',
    WHEEL_OF_FORTUNE_NOTIFICATION = 'wheel_of_fortune_notification',
}

export enum PostPublicationStatus {
    PENDING = 'pending',
    PUBLISHED = 'published',
    REJECTED = 'rejected',
    DRAFT = 'draft',
    ERROR = 'error',
}

export enum PostType {
    IMAGE = 'IMAGE',
    VIDEO = 'VIDEO',
    CAROUSEL = 'CAROUSEL',
    REEL = 'REEL',
}

export enum StoredInDBInsightsMetric {
    FOLLOWERS = 'followers',
    PLATFORM_RATING = 'platform_rating',
    EMAIL_CONTACTS = 'email_contacts',
    TEXT_MESSAGE_CLICKS = 'text_message_clicks',
    WEBSITE_CLICKS = 'website_clicks',
    IMPRESSIONS = 'impressions',
    POST_COUNT = 'post_count',
    PHONE_CALL_CLICKS = 'phone_call_clicks',
    BUSINESS_BOOKINGS = 'business_bookings',
    BUSINESS_FOOD_MENU_CLICKS = 'business_food_menu_clicks',
    DIRECTION_REQUESTS = 'direction_requests',
    BUSINESS_IMPRESSIONS_DESKTOP_MAPS = 'business_impressions_desktop_maps',
    BUSINESS_IMPRESSIONS_DESKTOP_SEARCH = 'business_impressions_desktop_search',
    BUSINESS_IMPRESSIONS_MOBILE_MAPS = 'business_impressions_mobile_maps',
    BUSINESS_IMPRESSIONS_MOBILE_SEARCH = 'business_impressions_mobile_search',
    BUSINESS_FOOD_ORDERS = 'business_food_orders',
    SHARES = 'shares',
    SAVES = 'saves',
}

// TODO: This should change in the near future to include the metric 'views' instead of 'impressions' as Facebook documentation states
// For the moment this is not the case, the 'views' metric is only available on direct call, not through webhook
// https://developers.facebook.com/blog/post/2025/01/21/making-it-easier-to-build-integrations-across-the-instagram-api-and-marketing-api/
// Developer forum thread regarding the issue: https://developers.facebook.com/community/threads/4032496000406458/
export enum IgWebhookStoryInsightsMetric {
    TAPS_BACK = 'taps_back',
    EXITS = 'exits',
    REACH = 'reach',
    IMPRESSIONS = 'impressions',
    TAPS_FORWARD = 'taps_forward',
    REPLIES = 'replies',
}

export enum IgLiveStoryInsightsMetric {
    TAP_BACK = 'tap_back',
    TAP_EXIT = 'tap_exit',
    TAP_FORWARD = 'tap_forward',
    SWIPE_FORWARD = 'swipe_forward',
}

export enum MalouMetric {
    IMPRESSIONS = 'impressions',
    ENGAGEMENTS = 'engagements',
    POSTS = 'posts',
    FOLLOWERS = 'followers',
    PLATFORM_RATING = 'platform_rating',
    QUERIES_DIRECT = 'queries_direct',
    QUERIES_INDIRECT = 'queries_indirect',
    ACTIONS_WEBSITE = 'actions_website',
    ACTIONS_PHONE = 'actions_phone',
    ACTIONS_DRIVING_DIRECTIONS = 'actions_driving_directions',
    BUSINESS_IMPRESSIONS_DESKTOP_MAPS = 'business_impressions_desktop_maps',
    BUSINESS_IMPRESSIONS_DESKTOP_SEARCH = 'business_impressions_desktop_search',
    BUSINESS_IMPRESSIONS_MOBILE_MAPS = 'business_impressions_mobile_maps',
    BUSINESS_IMPRESSIONS_MOBILE_SEARCH = 'business_impressions_mobile_search',
    BUSINESS_FOOD_ORDERS = 'business_food_orders',
    REACH = 'reach',
    TAPS_FORWARD = 'taps_forward',
    TAPS_BACK = 'taps_back',
    TAPS_EXITS = 'taps_exits',
    REPLIES = 'replies',
    ACTIONS_BOOKING_CLICK = 'actions_booking',
    ACTIONS_MENU_CLICK = 'actions_menu',
}

export enum AggregationType {
    TOTAL = 'total',
    AVERAGE = 'average',
    MAX = 'max',
    MIN = 'min',
}

export enum GmbPostsMetric {
    LOCAL_POST_VIEWS_SEARCH = 'LOCAL_POST_VIEWS_SEARCH',
    LOCAL_POST_ACTIONS_CALL_TO_ACTION = 'LOCAL_POST_ACTIONS_CALL_TO_ACTION',
}

export enum AggregationTimeScale {
    TOTAL = 'total',
    BY_DAY = 'by_day',
    BY_WEEK = 'by_week',
    BY_MONTH = 'by_month',
}

export const months = Object.freeze({
    fr: ['janvier', 'février', 'mars', 'avril', 'mai', 'juin', 'juillet', 'août', 'septembre', 'octobre', 'novembre', 'décembre'],
    en: ['january', 'february', 'march', 'april', 'may', 'june', 'july', 'august', 'september', 'october', 'november', 'december'],
});

export const scopes = Object.freeze({
    FACEBOOK: [
        'pages_read_engagement',
        'pages_show_list',
        'pages_read_user_content',
        'pages_manage_metadata',
        'pages_manage_engagement',
        'read_insights',
        'pages_manage_posts',
        'pages_messaging',
        'business_management',
    ],
    INSTAGRAM: [
        'instagram_basic',
        'pages_show_list',
        'instagram_manage_comments',
        'pages_manage_metadata',
        'pages_read_engagement',
        'instagram_manage_insights',
        'instagram_content_publish',
        'instagram_manage_messages',
        'pages_messaging',
        'business_management',
    ],
    FACEBOOK_PAGE_SCOPE: [
        'pages_read_engagement',
        'pages_show_list',
        'pages_read_user_content',
        'pages_manage_metadata',
        'pages_manage_engagement',
        'pages_manage_posts',
        'pages_messaging',
    ],
    INSTAGRAM_PAGE_SCOPE: [
        'instagram_basic',
        'pages_show_list',
        'instagram_manage_comments',
        'pages_manage_metadata',
        'pages_read_engagement',
        'instagram_manage_insights',
        'instagram_content_publish',
        'instagram_manage_messages',
        'pages_messaging',
    ],
    UBEREATS: ['eats.report', 'eats.store', 'eats.store.status.write'],
    GMB: [
        'https://www.googleapis.com/auth/business.manage',
        'https://www.googleapis.com/auth/userinfo.email',
        'https://www.googleapis.com/auth/businesscommunications', // TODO: check if needed
    ],
    TIKTOK_OAUTH_SCOPE: ['user.info.basic', 'user.info.profile', 'user.info.stats', 'video.publish', 'video.upload', 'video.list'],
});

export enum PermissionStatus {
    NOT_CONNECTED = 'not_connected',
    NO_ACCESS = 'no_access',
    AVAILABLE = 'available',
}

export const defaultLabels = Object.freeze([
    'moderation.answer_comment_modal.good_luck',
    'moderation.answer_comment_modal.come_back_soon',
    'moderation.answer_comment_modal.thank_you',
    'moderation.answer_comment_modal.thanks',
    'moderation.answer_comment_modal.your_welcome',
]);

export const eventsPermissions = Object.freeze([
    {
        key: 'canCreatePostInstagram',
        permission: {
            type: 'USER',
            platformKey: 'instagram',
            list: [
                ['instagram_basic', 'instagram_content_publish', 'public_profile', 'pages_read_engagement'],
                ['instagram_basic', 'instagram_content_publish', 'public_profile', 'pages_show_list'],
            ],
        },
    },
    {
        key: 'canCreatePostFacebook',
        permission: {
            type: 'PAGE',
            platformKey: 'facebook',
            list: [['pages_read_engagement', 'pages_manage_posts', 'pages_show_list', 'public_profile']],
        },
    },
    {
        key: 'canAnswerCommentInstagram',
        permission: {
            type: 'USER',
            platformKey: 'instagram',
            list: [['instagram_basic', 'instagram_manage_comments', 'pages_show_list', 'pages_read_engagement', 'public_profile']],
        },
    },
    {
        key: 'canAnswerCommentFacebook',
        permission: {
            type: 'PAGE',
            platformKey: 'facebook',
            list: [['pages_show_list', 'pages_read_user_content', 'pages_manage_engagement', 'public_profile']],
        },
    },
    {
        key: 'canAnswerReviewFacebook',
        permission: {
            type: 'PAGE',
            platformKey: 'facebook',
            list: [['pages_show_list', 'pages_read_user_content', 'pages_manage_engagement', 'public_profile']],
        },
    },
    {
        key: 'canSearchInspirationsInstagram',
        permission: {
            type: 'USER',
            platformKey: 'instagram',
            list: [
                ['instagram_basic', 'pages_read_engagement', 'public_profile', 'instagram_manage_insights'],
                ['instagram_basic', 'pages_show_list', 'public_profile', 'instagram_manage_insights'],
            ],
        },
    },
]);

export enum IgStoryType {
    REPLY_TO = 'reply_to',
    MENTION = 'mention',
}

export const igMessageReaction = 'love';

export enum FacebookMessageType {
    MESSAGE_TAG = 'MESSAGE_TAG',
    UPDATE = 'UPDATE',
    RESPONSE = 'RESPONSE',
}

export enum FacebookThumbUp {
    ReplaceText = '__THUMB_UP__',
    FbSocialId = '369239263222822',
}

export enum TemplateType {
    REVIEW = 'review',
    COMMENT = 'comment',
    MESSAGE = 'message',
}

export enum TemplateStatus {
    ACTIVE = 'active',
    PENDING = 'pending',
}

export enum ApiKeyRole {
    BUSINESS_PARTNER = 'business_partner',
    CLIENT = 'client',
    PUBLIC = 'public',
    INTERNAL_APP = 'internal_app',
}

export enum SimilarRestaurantCategory {
    ITALIAN_RESTAURANT = 'italian_restaurant',
    BRUNCH_BREAKFAST = 'brunch_breakfast',
    ASIAN_RESTAURANT = 'asian_restaurant',
    PIZZA_RESTAURANT = 'pizza_restaurant',
    BISTRO_PUB = 'bistro_pub',
    JAPANESE_RESTAURANT = 'japanese_restaurant',
    OCCIDENTAL_RESTAURANT = 'occidental_restaurant',
    BISTRONOMIC_RESTAURANT = 'bistronomic_restaurant',
    VEGETARIAN_VEGAN_RESTAURANT = 'vegetarian_vegan_restaurant',
    BAR_ENTERTAINMENT_VENUE = 'bar_entertainment_venue',
    COFFEE_TEA_ROOM = 'coffee_tea_room',
    GASTRONOMIC_RESTAURANT = 'gastronomic_restaurant',
    ORIENTAL_MEDITERRANEAN_RESTAURANT = 'oriental_mediterranean_restaurant',
    FISH_SEAFOOD_RESTAURANT = 'fish_seafood_restaurant',
    FAST_FOOD = 'fast_food',
    HEALTHY_FAST_FOOD = 'healthy_fast_food',
    CREPERIE = 'creperie',
    BAKERY = 'bakery',
    CATERER = 'caterer',
    GROCERY_MARKET = 'grocery_market',
    LATINO_AFRICAN_CARIBBEAN_RESTAURANT = 'latino_african_caribbean_restaurant',
    ACCOMMODATION_WELLNESS_VENUE = 'accommodation_wellness_venue',
    OTHER = 'other',
}

export const clientsFilesFormat = Object.freeze([
    {
        key: 'zenchef',
        headers: [
            'Prenom',
            'Nom',
            'Email',
            'Telephone',
            'Pays',
            'Statut destinataire',
            'Email optin market',
            'Date inscription mail',
            'SMS optin market',
            'Date inscription SMS',
            'Email optin review',
            'Date inscription avis par mail',
            'SMS review market',
            'Date inscription avis par SMS',
            'lang',
            'vip',
            'has_no_show',
            'is_new',
            'is_blacklisted',
            'retention_rate_label',
            'retention_rate',
            'has_opt_in_market_mail',
            'has_opt_in_review_mail',
            'created_at',
            'updated_at',
        ],
        required: ['Prenom', 'Nom', 'Telephone', 'Email'],
    },
    {
        key: 'pulp',
        headers: [''],
        required: [''],
    },
    {
        key: 'lafourchette',
        headers: [
            'Civilité',
            'Prénom',
            'Nom',
            'Pays',
            'Ville',
            'Code Postal',
            'Adresse',
            'Mobile Personnel',
            'Email personnel',
            'Langue',
            "Date d'inscription",
            'Newsletter',
            'Date de naissance',
            'Statut',
            'VIP',
            'Fax Personnel',
            "Complément d'adresse",
            'Prescripteur',
            'Commission négociée',
            "Nombre d'apport d'affaire total",
            "Chiffre d'affaire apporté total",
            'Entreprise',
            'Fonction',
            'Email Pro',
            'Mobile Pro',
            'Téléphone pro',
            'Pays Pro',
            'Adresse Pro',
            "Complément d'adresse Pro",
            'Ville Pro',
            'Allergie',
            'Nom Allergie',
            'Végétarien',
            'Handicap',
            'Préférence Nourriture',
            'Tables préférées',
            'Note',
            'Newsletter',
            'Restaurant de référence',
            'Date de mise à jour',
            "Date d'inscription",
            "Nombre d'annulation",
            'Nombre de no-show',
            'Total dépense',
        ],
        matchedHeaders: ['Dernière date de visite', 'Nombre de visites'],
        required: ['Prénom', 'Nom', 'Mobile Personnel', 'Email personnel'],
    },
    {
        key: 'zelty',
        headers: [
            'Nom',
            'Prénom',
            'N° de rue',
            'Ville',
            'Code postal',
            'Nom de rue',
            'N° de rue',
            'Téléphone',
            'Mail',
            "Date d'inscription",
            'Optin Mail',
            'CA actuel',
            'Optin SMS',
            'Statut',
            'Date de naissance',
            'Dernier restaurant',
            'Date de la dernière commande',
            'Nombre de commandes',
            'CA',
            'Points de fidélités',
            'Info interne',
            'Info client',
            "Complément d'adresse",
            'Batiment',
            'Porte',
            'Étage',
            'Digicode',
            'Identifiant externe',
            'Carte fidélité',
            'VIP',
            'Téléphone 2',
            'Entreprise',
            'ID',
        ],
        required: ['Nom', 'Prénom', 'Téléphone', 'Mail'],
    },
    {
        key: 'malou',
        headers: [
            'Civilité (H/F/NSP)',
            'Prénom',
            'Nom',
            'Pays',
            'Ville',
            'Code Postal',
            'Numéro de téléphone',
            'Email',
            'Langue parlée',
            'Date de la dernière réservation ou du click and collect',
            'Nombre de visites/réservations',
            'Accepte de recevoir des mails',
            'Accepte de recevoir des sms',
        ],
        required: ['Nom', 'Prénom', 'Numéro de téléphone', 'Email'],
    },
]);

export enum ClientFileError {
    EMPTY = 'empty_file',
    MISSING = 'missing_headers',
}

export enum SeoPostTopic {
    STANDARD = 'STANDARD',
    UNSPECIFIED = 'LOCAL_POST_TOPIC_TYPE_UNSPECIFIED',
    EVENT = 'EVENT',
    OFFER = 'OFFER',
    ALERT = 'ALERT',
}

export const ubereatsClientCredentials = {
    key: 'UBEREATS_CLIENT_CREDENTIALS',
    ttl: 2592000, // from ubereats doc: https://developer.uber.com/docs/eats/guides/authentication#example-response
};

export enum PrivatePlatforms {
    TOTEM = 'totem',
    CAMPAIGN = 'campaign',
}

export const hashtagCategoriesColor = [
    'rgb(219, 237, 219)',
    'rgb(227, 226, 224)',
    'rgb(238, 224, 218)',
    'rgb(250, 222, 201)',
    'rgb(253, 236, 200)',
    'rgb(211, 229, 239)',
    'rgb(232, 222, 238)',
    'rgb(245, 224, 233)',
    'rgb(255, 226, 221)',
];

export const hashtagGeneralColor = 'rgba(227, 226, 224, 0.5)';

export const emailRegex =
    // eslint-disable-next-line no-control-regex,max-len
    /[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?/i;

export const timeRegex = /^(0[0-9]|1[0-9]|2[0-4]):[0-5][0-9]$/;

export const urlRegex = /https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-zA-Z0-9()]{1,12}\b([-a-zA-Z0-9()@:%_+.~#?&//=]*)/i;
export const urlWithLocalHostRegex =
    /https?:\/\/(((www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-zA-Z0-9()]{1,12})|(localhost:[0-9]{1,5}))\b([-a-zA-Z0-9()@:%_+.~#?&//=]*)/i;
export const withOrWithoutHttpPartUrlRegex =
    /(?:https?:\/\/)?(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-zA-Z0-9()]{1,12}\b([-a-zA-Z0-9()@:%_+.~#?&//=]*)/i;

export const phoneRegex = /[+]?[(]?\s?[+]?\s?[0-9]{2,3}\s?[)]?[-\s.]?(([0-9]{3}[-\s.]?[0-9]{3,9})|(([0-9]{1,4}[-\s.]?){7}))/gi;
export const punctuationRegex = /[^a-zA-ZÀ-ú0-9]+/g;

export enum ReportType {
    DAILY_REVIEWS = 'daily_reviews',
    WEEKLY_REVIEWS = 'weekly_reviews',
    WEEKLY_PERFORMANCE = 'weekly_performance',
    MONTHLY_PERFORMANCE = 'monthly_performance',
}

export const ratingTags = {
    restaurant_well_prepared: {
        fr: 'Bien préparé',
        en: 'Well prepared',
    },
    restaurant_perfectly_cooked: {
        fr: 'Cuisson parfaite',
        en: 'Perfectly cooked',
    },
    restaurant_sustainable_packaging: {
        fr: 'Conditionnement durable',
        en: 'Sustainable packaging',
    },
    restaurant_excellent_quality: {
        fr: 'Excellente qualité',
        en: 'Excellent quality',
    },
    restaurant_healthy: {
        fr: 'Healthy',
        en: 'Healthy',
    },
    restaurant_tasty: {
        fr: 'Savoureux',
        en: 'Tasty',
    },
    restaurant_fast: {
        fr: 'Rapide',
        en: 'Fast',
    },
    restaurant_accommodating: {
        fr: 'Accomodant',
        en: 'Accomodating',
    },
    restaurant_good_packaging: {
        fr: 'Bon emballage',
        en: 'Good packaging',
    },
    restaurant_excellent_value: {
        fr: 'Excellente valeur',
        en: 'Excellent value',
    },
    restaurant_not_tasty: {
        fr: 'Pas très savoureux',
        en: 'Not tasty',
    },
    restaurant_not_worth_price: {
        fr: 'Ne vaut pas le prix',
        en: 'Not worth price',
    },
    restaurant_missed_request: {
        fr: 'Instructions non respectées',
        en: 'Missed request',
    },
    restaurant_creative: {
        fr: 'Créatif',
        en: 'Creative',
    },
    restaurant_delicate: {
        fr: 'Délicat',
        en: 'Delicate',
    },
    restaurant_too_slow: {
        fr: 'Trop lent',
        en: 'Too slow',
    },
    restaurant_poor_packaging: {
        fr: 'Emballage médiocre',
        en: 'Poor packaging',
    },
    restaurant_unsustainable_packaging: {
        fr: 'Conditionnement non durable',
        en: 'Unsustainable packaging',
    },
    restaurant_crispy: {
        fr: 'Croustillant',
        en: 'Crispy',
    },
    restaurant_hot: {
        fr: 'Chaud',
        en: 'Hot',
    },
    restaurant_good_portions: {
        fr: 'Portions généreuses',
        en: 'Good portions',
    },
    restaurant_great_value: {
        fr: 'Consistant',
        en: 'Great value',
    },
    restaurant_honest: {
        fr: 'Honnête',
        en: 'Honest',
    },
    restaurant_the_best: {
        fr: 'Le meilleur',
        en: 'The best',
    },
    restaurant_professional: {
        fr: 'Professionnel',
        en: 'Professional',
    },
    restaurant_correct_order: {
        fr: 'Bonne commande',
        en: 'Correct order',
    },
};

export enum GmbNotificationType {
    GOOGLE_UPDATE = 'GOOGLE_UPDATE',
    NEW_REVIEW = 'NEW_REVIEW',
    UPDATED_REVIEW = 'UPDATED_REVIEW',
    NEW_CUSTOMER_MEDIA = 'NEW_CUSTOMER_MEDIA',
}

export enum GmbOpeningStatus {
    OPEN = 'OPEN',
    CLOSED_TEMPORARILY = 'CLOSED_TEMPORARILY',
}

export const SURFACE_UNSPECIFIED = 'SURFACE_UNSPECIFIED';

export enum CommentDisplayMode {
    POST = 'post',
    LIST = 'list',
}

export enum FeedbackMessageType {
    TEXT = 'text',
    CLOSE = 'close',
    REOPEN = 'reopen',
}

export enum FeedbackMessageVisibility {
    ADMIN = 'admin',
    BASIC = 'basic',
}

export enum FeedbackMessageParticipantType {
    AUTHOR = 'author',
    COMMENTATOR = 'commentator',
    TAGGED = 'tagged',
    OTHER = 'other',
}

export enum MentionType {
    POST = 'post',
    COMMENT = 'comment',
}

export enum TemplateTag {
    CLIENT_NAME = '[{client_name}]',
    BUSINESS_NAME = '[{business_name}]',
    MY_FIRSTNAME = '[{my_firstname}]',
    PHONE = '[{phone}]',
    ADDRESS = '[{address}]',
    MENU_LINK = '[{menu_link}]',
    WEBSITE = '[{website}]',
    REGULAR_HOURS = '[{regular_hours}]',
}

export enum MediaConvertedStatus {
    ERROR = 'error',
    COMPLETE = 'complete',
    PROGRESSING = 'progressing',
}

export const mediaConvertedStatusLabels = ['error', 'complete', 'progressing', null];

export enum DateStringFormat {
    FR = 'fr',
    EN = 'en',
}

export enum JobStatus {
    PENDING = 'pending',
    RUNNING = 'running',
    DONE = 'done',
    FAILED = 'failed',
}

export enum FoundStatusOnPlatform {
    FOUND = 'found',
    NOT_FOUND = 'not_found',
}

export const adminCredentialAuthIds = ['<EMAIL>', '232142364148787', '107323322263317', '122094862952058513'];

export const LANGUAGES = {
    'zh-CN': 'chinese (simplified)',
    'zh-TW': 'chinese (traditional)',
    af: 'afrikaans',
    am: 'amharic',
    ar: 'arabic',
    az: 'azerbaijani',
    be: 'belarusian',
    bg: 'bulgarian',
    bn: 'bengali',
    bs: 'bosnian',
    ca: 'catalan',
    ceb: 'cebuano',
    co: 'corsican',
    cs: 'czech',
    cy: 'welsh',
    da: 'danish',
    de: 'german',
    el: 'greek',
    en: 'english',
    eo: 'esperanto',
    es: 'spanish',
    et: 'estonian',
    eu: 'basque',
    fa: 'persian',
    fi: 'finnish',
    fr: 'french',
    fy: 'frisian',
    ga: 'irish',
    gd: 'scots gaelic',
    gl: 'galician',
    gu: 'gujarati',
    ha: 'hausa',
    haw: 'hawaiian',
    he: 'hebrew',
    hi: 'hindi',
    hmn: 'hmong',
    hr: 'croatian',
    ht: 'haitian creole',
    hu: 'hungarian',
    hy: 'armenian',
    id: 'indonesian',
    ig: 'igbo',
    is: 'icelandic',
    it: 'italian',
    iw: 'hebrew',
    ja: 'japanese',
    jw: 'javanese',
    ka: 'georgian',
    kk: 'kazakh',
    km: 'khmer',
    kn: 'kannada',
    ko: 'korean',
    ku: 'kurdish (kurmanji)',
    ky: 'kyrgyz',
    la: 'latin',
    lb: 'luxembourgish',
    lo: 'lao',
    lt: 'lithuanian',
    lv: 'latvian',
    mg: 'malagasy',
    mi: 'maori',
    mk: 'macedonian',
    ml: 'malayalam',
    mn: 'mongolian',
    mr: 'marathi',
    ms: 'malay',
    mt: 'maltese',
    my: 'myanmar (burmese)',
    ne: 'nepali',
    nl: 'dutch',
    no: 'norwegian',
    ny: 'chichewa',
    or: 'odia',
    pa: 'punjabi',
    pl: 'polish',
    ps: 'pashto',
    pt: 'portuguese',
    ro: 'romanian',
    ru: 'russian',
    sd: 'sindhi',
    si: 'sinhala',
    sk: 'slovak',
    sl: 'slovenian',
    sm: 'samoan',
    sn: 'shona',
    so: 'somali',
    sq: 'albanian',
    sr: 'serbian',
    st: 'sesotho',
    su: 'sundanese',
    sv: 'swedish',
    sw: 'swahili',
    ta: 'tamil',
    te: 'telugu',
    tg: 'tajik',
    th: 'thai',
    tl: 'filipino',
    tr: 'turkish',
    ug: 'uyghur',
    uk: 'ukrainian',
    ur: 'urdu',
    uz: 'uzbek',
    vi: 'vietnamese',
    xh: 'xhosa',
    yi: 'yiddish',
    yo: 'yoruba',
    zu: 'zulu',
};

export const AI_SOFT_LIMIT_CALL_COUNT = 10000;
export const AI_HARD_LIMIT_CALL_COUNT = 20000;

export enum NfcStar {
    ONE = 1,
    TWO = 2,
    THREE = 3,
    FOUR = 4,
    FIVE = 5,
}

export enum AiTextToOptimizeType {
    REVIEW_ANSWER = 'REVIEW_ANSWER',
    SOCIAL_NETWORK_POST = 'SOCIAL_NETWORK_POST',
    SEO_POST = 'SEO_POST',
}

export enum AiInteractionRelatedEntityCollection {
    POSTS = 'POSTS',
    REVIEWS = 'REVIEWS',
    RESTAURANTS = 'RESTAURANTS',
    REPORTS = 'REPORTS',
    KEYWORDS = 'KEYWORDS',
    HASHTAGS = 'HASHTAGS',
    SIMILAR_RESTAURANTS = 'SIMILAR_RESTAURANTS',
    RESTAURANT_AI_SETTINGS = 'RESTAURANT_AI_SETTINGS',
    DIAGNOSTICS = 'DIAGNOSTICS',
    MEDIA = 'MEDIA',
    CATEGORIES = 'CATEGORIES',
    STORE_LOCATOR_RESTAURANT_PAGE = 'STORE_LOCATOR_RESTAURANT_PAGE',
    SEGMENT_ANALYSIS_PARENT_TOPICS = 'SEGMENT_ANALYSIS_PARENT_TOPICS',
    SEGMENT_ANALYSES = 'SEGMENT_ANALYSES',
}

export enum InvalidPlatformReason {
    IG_NOT_FOUND = 'ig_not_found',
    IG_NOT_BUSINESS = 'ig_not_business',
    GMB_DISCONNECTED = 'gmb_disconnected',
    GMB_DISABLED = 'gmb_disabled',
    GMB_SUSPENDED = 'gmb_suspended',
    GMB_NOT_VERIFIED = 'gmb_not_verified',
    GMB_PENDING_VERIFICATION = 'gmb_pending_verification',
    TRIPADVISOR_NOT_MANAGER = 'tripadvisor_not_manager',
    TRIPADVISOR_UNCLAIMED_PAGE = 'tripadvisor_unclaimed_page',
    NEED_REVIEW = 'need_review',
    FB_PASSWORD_CHANGED = 'fb_password_changed',
    FB_LOGIN_AND_FOLLOW_INSTRUCTIONS = 'fb_login_and_follow_instructions',
    FB_NOT_CONFIRMED_USER = 'fb_not_confirmed_user',
    FB_USER_NEED_TO_BE_ADMIN_OR_MODERATOR_OR_EDITOR = 'fb_user_need_to_be_admin_or_moderator_or_editor',
    FB_UNABLE_DOWNLOAD_PHOTOS = 'fb_unable_download_photos',
    FB_USER_HAS_NOT_AUTHORIZED_APP = 'fb_user_has_not_authorized_app',
    UNKNOWN = 'unknown',
}

export enum AiInteractionType {
    ANSWER_REVIEW_ADVANCED_SETTINGS = 'answer_review_advanced_settings',
    GENERATE_SOCIAL_NETWORK_POST = 'generate_social_network_post',
    GENERATE_SOCIAL_NETWORK_POST_ADVANCED_SETTINGS = 'generate_social_network_post_advanced_settings',
    GENERATE_SEO_POST = 'generate_seo_post',
    GENERATE_SEO_POST_ADVANCED_SETTINGS = 'generate_seo_post_advanced_settings',
    CHOOSE_POST_HASHTAGS = 'choose_post_hashtags',
    OPTIMIZE_SOCIAL_NETWORK_POST = 'optimize_social_network_post',
    OPTIMIZE_SOCIAL_NETWORK_POST_ADVANCED_SETTINGS = 'optimize_social_network_post_advanced_settings',
    OPTIMIZE_SEO_POST = 'optimize_seo_post',
    OPTIMIZE_SEO_POST_ADVANCED_SETTINGS = 'optimize_seo_post_advanced_settings',
    OPTIMIZE_REVIEW_ANSWER_ADVANCED_SETTINGS = 'optimize_review_answer_advanced_settings',
    BREAKDOWN_AND_CLASSIFY_KEYWORDS = 'breakdown_and_classify_keywords',
    GENERATE_SEMANTIC_ANALYSIS_OVERVIEW = 'generate_semantic_analysis_overview',
    FRENCH_KEYWORDS_GENERATION = 'french_keywords_generation',
    ENGLISH_KEYWORDS_GENERATION = 'english_keywords_generation',
    ITALIAN_KEYWORDS_GENERATION = 'italian_keywords_generation',
    SPANISH_KEYWORDS_GENERATION = 'spanish_keywords_generation',
    CATEGORIZE_RESTAURANT = 'categorize_restaurant',
    IDENTIFY_SIMILAR_LOCALITIES = 'identify_similar_localities',
    IDENTIFY_SIMILAR_LOCALITIES_FOR_DIAGNOSTIC = 'identify_similar_localities_for_diagnostic',
    REVIEW_TRANSLATION = 'review_translation',
    POST_TRANSLATION = 'post_translation',
    REVIEW_ANSWER_TRANSLATION = 'review_answer_translation',
    INTRODUCTIVE_TRANSLATION = 'introductive_translation',
    SIGNATURE_TRANSLATION = 'signature_translation',
    GENERATE_IMAGE_DESCRIPTION = 'generate_image_description',
    TRANSLATE_TAGS = 'translate_tags',
    GENERATE_SEO_DUPLICATION = 'generate_seo_duplication',
    POST_ADVANCED_SETTINGS_DETECTION = 'post_advanced_settings_detection',
    GENERATE_RS_DUPLICATION = 'generate_rs_duplication',
    REVIEW_RELATED_BRICKS_DETECTION = 'review_related_bricks_detection',
    REVIEW_SEMANTIC_ANALYSIS = 'review_semantic_analysis',
    TOPIC_TRANSLATION = 'topic_translation',
    REVIEW_SEMANTIC_ANALYSIS_AND_TOPIC_LINKING = 'review_semantic_analysis_and_topic_linking',
    REVIEW_SEMANTIC_ANALYSIS_AND_TOPIC_LINKING_V2 = 'review_semantic_analysis_and_topic_linking_v2',
    TOPIC_LINKING = 'topic_linking',
    TOPIC_LINKING_V2 = 'topic_linking_v2',
    TOPIC_CREATION = 'topic_creation',
    REVIEW_ITEM_DETECTION = 'review_item_detection',
    TRANSLATION = 'translation',
    LANGUAGE_DETECTION = 'language_detection',
    ALTERNATIVE_TEXT = 'alternative_text',
    INTELLIGENT_SUBJECTS_DETECTION = 'intelligent_subjects_detection',
    BRAND_KEYWORDS_IDENTIFICATION = 'brand_keywords_identification',

    // Deprecated : new interactions from sept 2024 should not use/have these types
    TRANSLATE = 'translate',
    ANSWER_REVIEW = 'answer_review',
    OPTIMIZE_REVIEW_ANSWER = 'optimize_review_answer',
}

export enum AiInteractionSubType {
    ANSWER_POSITIVE_REVIEW_WITH_COMMENT_ADVANCED_SETTINGS = 'answer_positive_review_with_comment_advanced_settings',
    ANSWER_POSITIVE_REVIEW_WITHOUT_COMMENT_ADVANCED_SETTINGS = 'answer_positive_review_without_comment_advanced_settings',
    ANSWER_NEGATIVE_REVIEW_WITH_COMMENT_ADVANCED_SETTINGS = 'answer_negative_review_with_comment_advanced_settings',
    ANSWER_NEGATIVE_REVIEW_WITHOUT_COMMENT_ADVANCED_SETTINGS = 'answer_negative_review_without_comment_advanced_settings',
}

export enum PostError {
    POST_ALREADY_PUBLISHED = 'post_already_published',
    POST_STUCK_PROCESSING = 'post_stuck_processing',
    STORY_NOT_PUBLISHED_BECAUSE_PREVIOUS_ONE_WAS_NOT_PUBLISHED = 'story_not_published_because_previous_one_was_not_published',
}

export enum SizeInBytes {
    KILO_BYTES = 2 ** 10,
    MEGA_BYTES = 2 ** 20,
    GIGA_BYTES = 2 ** 30,
}

export enum InformationUpdateStatus {
    PENDING = 'PENDING',
    VALIDATED = 'VALIDATED',
}

export enum InformationUpdatePlatformStateStatus {
    TO_BE_SENT = 'TO_BE_SENT',
    ERROR = 'ERROR',
    BAD_ACCESS = 'BAD_ACCESS',
    UNCLAIMED_PAGE = 'UNCLAIMED_PAGE',
    INVALID_PAGE = 'INVALID_PAGE',
    MANUAL_UPDATE_ERROR = 'MANUAL_UPDATE_ERROR',
    PENDING = 'PENDING',
    DONE = 'DONE',
}

export enum InformationUpdateAttributeValue {
    YES = 'yes',
    NO = 'no',
}

export enum OpenaiModelVersion {
    DEFAULT_MODEL = 'gpt-3.5-turbo-instruct',
    GPT_3_5_TURBO_1106 = 'gpt-3.5-turbo-1106',
}

export enum RestaurantAttributeValue {
    NOT_CONCERNED = 'notConcerned',
    YES = 'yes',
    NO = 'no',
}

export enum BrickType {
    CATEGORY_TO_SPECIAL = 'category_to_special',
    CATEGORY_TO_TYPE = 'category_to_type',
    POSTAL_CODE_TO_TOURISTIC_AREA = 'postal_code_to_touristic_area',
}

export const USER_AGENTS = [
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.83 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.87 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0.3 Safari/605.1.15',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 11_1_0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.192 Safari/537.36',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/42.0.2311.135 Safari/537.36 Edge/12.246',
    'Mozilla/5.0 (X11; CrOS x86_64 8172.45.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.64 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_11_2) AppleWebKit/601.3.9 (KHTML, like Gecko) Version/9.0.2 Safari/601.3.9',
    'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/47.0.2526.111 Safari/537.36',
    'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:15.0) Gecko/20100101 Firefox/15.0.1',
];

export enum FileType {
    CSV = 'csv',
    XLS = 'xls',
    XLSX = 'xlsx',
    DOC = 'doc',
    DOCX = 'docx',
    PDF = 'pdf',
    TXT = 'txt',
    RTF = 'rtf',
    ODT = 'odt',
}

export enum MediaFileType {
    IMAGE = 'image',
    VIDEO = 'video',
    AUDIO = 'audio',
    DOCUMENT = 'document',
}

// ############################################

export const langDomaineMapping = Object.freeze({
    fr: { tld: 'fr' },
    en: { tld: 'com' },
    pt: { tld: 'pt' },
    de: { tld: 'de' },
    zhTW: { tld: 'cn' },
    es: { tld: 'es' },
    it: { tld: 'it' },
    no: { tld: 'no' },
    ja: { tld: 'jp' },
    ru: { tld: 'ru' },
    da: { tld: 'dk' },
    tr: { tld: 'tr' },
    fi: { tld: 'fi' },
    sv: { tld: 'se' },
    ko: { tld: 'kr' },
    in: { tld: 'id' },
    el: { tld: 'gr' },
    cs: { tld: 'cz' },
    vi: { tld: 'vn' },
    th: { tld: 'th' },
    pl: { tld: 'pl' },
    nl: { tld: 'nl' },
    sk: { tld: 'sk' },
});

export enum ChatCompletionRole {
    SYSTEM = 'system',
    USER = 'user',
}

export interface ILatlng {
    lat: number;
    lng: number;
}

export enum InsightsTab {
    // todo: remove SEO when release-keywords-insights-v2 feature flag is removed
    SEO = 'seo',
    SEO_KEYWORDS = 'seo_keywords',
    SEO_IMPRESSIONS = 'seo_impressions',
    E_REPUTATION = 'e_reputation',
    E_REPUTATION_WITH_NEW_SEMANTIC_ANALYSIS = 'e_reputation_with_new_semantic_analysis',
    SOCIAL_NETWORKS = 'social_networks',
    BOOSTERS = 'boosters',
    AGGREGATED_SEO = 'aggregated_seo',
    SUMMARY = 'summary',
    // remove AGGREGATED_SEO_KEYWORDS when release-aggregated-keywords-insights-v2 feature flag is removed
    AGGREGATED_SEO_KEYWORDS = 'aggregated_seo_keywords',
    AGGREGATED_SEO_KEYWORDS_V2 = 'aggregated_seo_keywords_v2',
    AGGREGATED_SEO_IMPRESSIONS = 'aggregated_seo_impressions',
    AGGREGATED_E_REPUTATION = 'aggregated_e_reputation',
    AGGREGATED_E_REPUTATION_WITH_NEW_SEMANTIC_ANALYSIS = 'aggregated_e_reputation_with_new_semantic_analysis',
    AGGREGATED_SOCIAL_NETWORKS = 'aggregated_social_networks',
    AGGREGATED_BOOSTERS = 'aggregated_boosters',
    AGGREGATED_SUMMARY = 'aggregated_summary',
}

export const aggregatedInsightsTabs = [
    InsightsTab.AGGREGATED_SEO,
    InsightsTab.AGGREGATED_E_REPUTATION,
    InsightsTab.AGGREGATED_SOCIAL_NETWORKS,
    InsightsTab.AGGREGATED_BOOSTERS,
];

export enum InsightsChart {
    // seo
    ACTIONS = 'actions',
    APPARITIONS = 'apparitions',
    KEYWORDS = 'keywords',
    KEYWORD_SEARCH_IMPRESSIONS = 'keyword_search_impressions',
    // e-reputation
    REVIEW_RATINGS_KPIS = 'review_ratings_kpis',
    REVIEW_KPIS = 'review_kpis',
    REVIEW_RATING_EVOLUTION = 'review_rating_evolution',
    REVIEW_RATING_TOTAL = 'review_rating_total',
    REVIEW_ANALYSES_TAG_CHARTS = 'review_analyses_tag_charts',
    REVIEW_ANALYSES_TAG_EVOLUTION = 'review_analyses_tag_evolution',
    SEMANTIC_ANALYSIS_TOP_TOPICS = 'semantic_analysis_top_topics',
    SEMANTIC_ANALYSIS_TOPICS_EVOLUTION = 'semantic_analysis_topics_evolution',
    SEMANTIC_ANALYSIS_REVIEWS = 'semantic_analysis_reviews',
    // social network
    COMMUNITY = 'community',
    ENGAGEMENT = 'engagement',
    POST_INSIGHTS = 'post_insights',
    REEL_INSIGHTS = 'reel_insights',
    STORY_INSIGHTS = 'story_insights',
    // boosters
    BOOSTERS_PRIVATE_REVIEWS_COUNT = 'boosters_private_reviews_count',
    BOOSTERS_SCAN_COUNT = 'boosters_scan_count',
    BOOSTERS_TOTEMS_ESTIMATED_REVIEWS_COUNT = 'boosters_totems_estimated_reviews_count',
    BOOSTERS_WHEEL_OF_FORTUNE_GIFTS_DISTRIBUTION = 'boosters_wheel_of_fortune_gifts_distribution',
    // aggregated seo
    AGGREGATED_RANKINGS = 'aggregated_rankings',
    AGGREGATED_ACTIONS = 'aggregated_actions',
    AGGREGATED_APPARITIONS = 'aggregated_apparitions',
    AGGREGATED_TOP_SEARCH_KEYWORDS = 'aggregated_top_search_keywords',
    // aggregated e-reputation
    AGGREGATED_REVIEW_RATINGS_KPIS = 'aggregated_review_ratings_kpis',
    AGGREGATED_REVIEWS_COUNT = 'aggregated_reviews_count',
    AGGREGATED_REVIEWS_RATING_AVERAGE = 'aggregated_reviews_rating_average',
    AGGREGATED_REVIEW_ANALYSES_TAG_CHARTS = 'aggregated_review_analyses_tag_charts',
    AGGREGATED_REVIEW_ANALYSES_TAG_EVOLUTION = 'aggregated_review_analyses_tag_evolution',
    AGGREGATED_SEMANTIC_ANALYSIS_AVERAGE_BY_CATEGORY = 'aggregated_semantic_analysis_average_by_category',
    AGGREGATED_SEMANTIC_ANALYSIS_CHART = 'aggregated_semantic_analysis_chart',
    AGGREGATED_SEMANTIC_ANALYSIS_BREAKDOWN_BY_RESTAURANT = 'aggregated_semantic_analysis_breakdown_by_restaurant',
    // social network
    AGGREGATED_TOP_POSTS_CARDS = 'aggregated_top_posts_cards',
    AGGREGATED_PUBLICATIONS_TABLE = 'aggregated_publications_table',
    // aggregated boosters
    AGGREGATED_BOOSTERS_SCAN_COUNT = 'aggregated_boosters_scan_count',
    AGGREGATED_BOOSTERS_PRIVATE_REVIEWS_COUNT = 'aggregated_boosters_private_reviews_count',
    AGGREGATED_BOOSTERS_REVIEWS_COUNT = 'aggregated_boosters_reviews_count',
    AGGREGATED_BOOSTERS_WHEEL_OF_FORTUNE_GIFTS_DISTRIBUTION = 'aggregate_boosters_wheel_of_fortune_gifts_distribution',
    AGGREGATED_BOOSTERS_WHEEL_OF_FORTUNE_ESTIMATED_REVIEWS_COUNT = 'aggregated_boosters_wheel_of_fortune_estimated_reviews_count',
}

export enum CsvInsightChart {
    // seo
    KEYWORDS = 'keywords',
    GMB_VISIBILITY = 'gmb_visibility',
    KEYWORD_SEARCH_IMPRESSIONS = 'keyword_search_impressions',
    // e-reputation
    REVIEWS_RATINGS_EVOLUTION = 'reviews_ratings_evolution',
    REVIEWS_RATINGS_TOTAL = 'reviews_ratings_total',
    PLATFORMS_RATINGS = 'platforms_ratings',
    SEMANTIC_ANALYSIS_TOPICS = 'semantic_analysis_topics',
    SEMANTIC_ANALYSIS_DETAILS = 'semantic_analysis_details',
    // e-reputation
    AGGREGATED_PLATFORMS_RATINGS = 'aggregated_platforms_ratings',
    AGGREGATED_SEMANTIC_ANALYSIS_TOP_TOPICS = 'aggregated_semantic_analysis_top_topics',
    AGGREGATED_SEMANTIC_ANALYSIS_BY_CATEGORY = 'aggregated_semantic_analysis_by_category',
    // aggregated seo
    AGGREGATED_GMB_VISIBILITY = 'aggregated_gmb_visibility',
    AGGREGATED_RANKINGS = 'aggregated_rankings',
    AGGREGATED_TOP_SEARCH_KEYWORDS = 'aggregated_top_search_keywords',
    // social network
    PUBLICATIONS = 'publications',
    STORIES = 'stories',
    ALL_FOLLOWERS = 'all_followers',
    FB_FOLLOWERS = 'fb_followers',
    IG_FOLLOWERS = 'ig_followers',
    // boosters
    BOOSTERS_SCAN_COUNT = 'boosters_scan_count',
    BOOSTERS_REVIEWS_COUNT = 'boosters_reviews_count',
    BOOSTERS_WHEEL_OF_FORTUNE_GIFTS_DISTRIBUTION = 'boosters_wheel_of_fortune_gifts_distribution',
    // aggregated social network
    AGGREGATED_ALL_FOLLOWERS = 'aggregated_all_followers',
    AGGREGATED_FB_FOLLOWERS = 'aggregated_fb_followers',
    AGGREGATED_IG_FOLLOWERS = 'aggregated_ig_followers',
    AGGREGATED_PUBLICATIONS = 'aggregated_publications',
    AGGREGATED_STORIES = 'aggregated_stories',
    AGGREGATED_REVIEW_COUNT = 'aggregated_review_count',
    AGGREGATED_AVERAGE_REVIEWS_RATINGS = 'aggregated_average_reviews_ratings',
    // aggregated boosters
    AGGREGATED_BOOSTERS_SCAN_COUNT = 'aggregated_boosters_scan_count',
    AGGREGATED_BOOSTERS_REVIEWS_COUNT = 'aggregated_boosters_reviews_count',
    AGGREGATED_BOOSTERS_PRIVATE_REVIEWS_COUNT = 'aggregated_boosters_private_reviews_count',
    AGGREGATED_BOOSTERS_WHEEL_OF_FORTUNE_GIFTS_DISTRIBUTION = 'aggregated_boosters_wheel_of_fortune_gifts_distribution',
    // SUMMARY
    INSIGHTS_SUMMARY = 'insights_summary',
    AGGREGATED_INSIGHTS_SUMMARY = 'aggregated_insights_summary',
}

export const openTableCookieKey = 'opentable-cookie-otumamiauth';

export enum AppEntity {
    RESTAURANTS = 'restaurants',
    USERS = 'users',
}

export enum PdfFileFormat {
    /** 216 x 279 mm */
    LETTER = 'Letter',
    /** 216 x 356 mm */
    LEGAL = 'Legal',
    /** 279 x 432 mm */
    TABLOID = 'Tabloid',
    /** 210 x 297 mm */
    LEDGER = 'Ledger',
    /** 841 x 1189 mm */
    A0 = 'A0',
    /** 594 x 841 mm */
    A1 = 'A1',
    /** 420 x 594 mm  */
    A2 = 'A2',
    /** 297 x 420 mm */
    A3 = 'A3',
    /** 210 x 297 mm */
    A4 = 'A4',
    /** 148 x 210 mm */
    A5 = 'A5',
    /** 105 x 148 mm */
    A6 = 'A6',
}

export enum Currency {
    EUR = 'EUR', // euro
    USD = 'USD', // dollar
    GBP = 'GBP', // pound
}

export enum HashtagType {
    RESTAURANT = 'restaurant',
    DESCRIPTION = 'description',
    UNKNOWN = 'unknown',
}

export enum WatcherStatus {
    RUNNING = 'running',
    FINISHED = 'finished',
    FAILED = 'failed',
}

export enum PlatformDataFetchedStatus {
    PENDING = 'pending',
    ERROR = 'error',
    SUCCESS = 'success',
    ASYNC = 'async',
}

export enum MediaTagCategory {
    FOOD = 'food',
    DRINKS = 'drinks',
    LOCATION = 'location',
    SPECIAL_EVENT = 'specialEvent',
    PEOPLE = 'people',
    OTHER = 'other',
}

export enum MediaTagSubcategory {
    MAIN = 'main',
    DESSERT = 'dessert',
    BACKGROUND_TYPE = 'backgroundType',
    LANDMARK = 'landmark',
}

export enum TooltipPosition {
    TOP = 'top',
    BOTTOM = 'bottom',
    LEFT = 'left',
    RIGHT = 'right',
}
