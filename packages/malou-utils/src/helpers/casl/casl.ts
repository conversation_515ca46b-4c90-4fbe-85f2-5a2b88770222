import { CaslRole, Role, UserCaslRole } from '../../constants';

export const getAbilityRoleFromUserCaslRole = ({
    userAppRole,
    userCaslRole,
}: {
    userAppRole: Role;
    userCaslRole: UserCaslRole;
}): UserCaslRole => {
    if (userAppRole === Role.ADMIN) {
        return UserCaslRole.ADMIN; // bypass everything if user has admin role
    }
    return userCaslRole || UserCaslRole.GUEST;
};

export const getAbilityRoleFromUserRestaurantCaslRole = ({
    userAppRole,
    userRestaurantCaslRole,
}: {
    userAppRole: Role;
    userRestaurantCaslRole: CaslRole;
}): CaslRole => {
    if (userAppRole === Role.ADMIN) {
        return CaslRole.ADMIN; // bypass everything if user has admin role
    }
    return userRestaurantCaslRole || CaslRole.GUEST;
};
