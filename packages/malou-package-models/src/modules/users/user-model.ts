import bcrypt from 'bcryptjs';
import { FromSchema } from 'json-schema-to-ts';
import jwt from 'jsonwebtoken';
import { DateTime } from 'luxon';
import { ObjectId } from 'mongodb';
import mongoose from 'mongoose';

import { DESERIALIZE_OPTIONS } from ':core/mongoose-json-schema/definitions/constants';
import { createMongooseSchemaFromJSONSchema } from ':core/mongoose-json-schema/definitions/jsonschema-to-mongoose';
import { FeedbackModel } from ':modules/feedbacks/feedback-model';
import { IMedia } from ':modules/media/media-model';

import { userJSONSchema } from './user-schema';

const userSchema = createMongooseSchemaFromJSONSchema(userJSONSchema);

userSchema.virtual('settings.receiveMessagesNotifications.restaurants', {
    ref: 'Restaurant',
    localField: 'settings.receiveMessagesNotifications.restaurantsIds',
    foreignField: '_id',
    justOne: false,
});

userSchema.virtual('restaurants', {
    ref: 'UserRestaurant',
    localField: '_id',
    foreignField: 'userId',
    justOne: false,
});

userSchema.virtual('organizations', {
    ref: 'Organization',
    localField: 'organizationIds',
    foreignField: '_id',
    justOne: false,
});

userSchema.virtual('createdByUser', {
    ref: 'User',
    localField: 'createdByUserId',
    foreignField: '_id',
    justOne: true,
});

userSchema.index({ 'settings.receiveMessagesNotifications.active': 1, 'settings.receiveMessagesNotifications.restaurantsIds': 1 });
userSchema.index({ providerId: 1 }, { unique: true, sparse: true });

userSchema.pre('save', function (this: mongoose.Document & IUser, next) {
    // password
    if (this.isModified('password') || this.isNew) {
        try {
            const salt = bcrypt.genSaltSync(10);
            const hash = bcrypt.hashSync(this.password, salt);
            this.password = hash;
            next();
        } catch (error) {
            next(error as Error);
        }
    } else {
        next();
    }
});

userSchema.pre('updateOne', function (this: mongoose.Document & IUser, next) {
    const update = (this as any)._update;
    if (update.password) {
        try {
            const salt = bcrypt.genSaltSync(10);
            const hash = bcrypt.hashSync(update.password, salt);
            this.set({ password: hash });
            next();
        } catch (error) {
            next(error as Error);
        }
    } else {
        next();
    }
});

userSchema.pre('findOneAndUpdate', function (next) {
    const update = (this as any)._update;
    if (update.password) {
        try {
            const salt = bcrypt.genSaltSync(10);
            const hash = bcrypt.hashSync(update.password, salt);
            this.set({ password: hash });
            next();
        } catch (error) {
            next(error as Error);
        }
    } else {
        next();
    }
});

userSchema.post('updateOne', async function (next) {
    try {
        const update = (this as any).getFilter();
        const user = await UserModel.findById(update._id).populate<{ profilePicture: IMedia }>('profilePicture');
        if (!user) {
            return;
        }
        await FeedbackModel.updateMany(
            { 'participants.participant._id': new ObjectId(user._id) },
            {
                'participants.$.participant.name': user.name,
                'participants.$.participant.lastname': user.lastname,
                'participants.$.participant.profilePictureUrl': user.profilePicture?.urls?.small,
                'participants.$.participant.email': user.email,
            }
        );

        await FeedbackModel.updateMany(
            { 'feedbackMessages.author._id': new ObjectId(user._id) },
            {
                'feedbackMessages.$[elem].author.name': user.name,
                'feedbackMessages.$[elem].author.lastname': user.lastname,
                'feedbackMessages.$[elem].author.profilePictureUrl': user.profilePicture?.urls?.small,
                'feedbackMessages.$[elem].author.email': user.email,
            },
            { arrayFilters: [{ 'elem.author._id': new ObjectId(user._id) }], multi: true }
        );
    } catch (err) {
        console.error('error updating feedbacks user', err);
    }
});

userSchema.methods.generateJwt = function () {
    const expiry = DateTime.now().plus({ days: 365 }).toJSDate();
    const payload = {
        _id: this._id,
        exp: expiry.getTime() / 1000,
    };
    return jwt.sign(payload, process.env.PASSPORT_SECRET ?? 'secret');
};

userSchema.methods.comparePassword = function (passw: string, cb: (e: Error | null, matches?: boolean) => void) {
    try {
        const matches = bcrypt.compareSync(passw, this.password);
        return cb(null, matches);
    } catch (err) {
        return cb(err as Error);
    }
};

// Handle 11000 duplicate key
userSchema.post('save', (error: Error, doc: unknown, next: (error?: Error) => void): void => {
    if ('code' in error && error.code === 11000) {
        const err = new Error('User already exists');
        next(err);
    } else {
        next(error);
    }
});

export type IUser = FromSchema<
    typeof userJSONSchema,
    {
        keepDefaultedPropertiesOptional: true;
        deserialize: DESERIALIZE_OPTIONS;
    }
>;

export const UserModel = mongoose.model<IUser>(userJSONSchema.title, userSchema);
