import { FromSchema } from 'json-schema-to-ts';
import { MongoError } from 'mongodb';
import mongoose from 'mongoose';

import { DESERIALIZE_OPTIONS } from ':core/mongoose-json-schema/definitions/constants';
import { createMongooseSchemaFromJSONSchema } from ':core/mongoose-json-schema/definitions/jsonschema-to-mongoose';

import { organizationJSONSchema } from './organization-schema';

const organizationSchema = createMongooseSchemaFromJSONSchema(organizationJSONSchema);

organizationSchema.index({ name: 1 }, { unique: true });
organizationSchema.index({ providerId: 1 }, { unique: true, sparse: true });

organizationSchema.virtual('storeLocatorConfig', {
    ref: 'StoreLocatorOrganizationConfig',
    localField: '_id',
    foreignField: 'organizationId',
    justOne: true,
});

organizationSchema.post('save', async (error: Error, doc: unknown, next: (error?: Error) => void): Promise<void> => {
    if (error instanceof MongoError && error.code === 11000) {
        next(new Error('Organization already exists'));
        return;
    }
    next(error);
});

export type IOrganization = FromSchema<
    typeof organizationJSONSchema,
    {
        keepDefaultedPropertiesOptional: true;
        deserialize: DESERIALIZE_OPTIONS;
    }
>;

export const OrganizationModel = mongoose.model<IOrganization>(organizationJSONSchema.title, organizationSchema);
